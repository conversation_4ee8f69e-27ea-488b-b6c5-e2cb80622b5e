{"mcpServers": {"fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "disabled": false, "autoApprove": []}, "firecrawl-mcp": {"command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "fc-4c015d38a5d84299b3041a62fe373aad"}, "disabled": false, "autoApprove": ["firecrawl_scrape", "firecrawl_search"]}, "Context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {}, "disabled": false, "autoApprove": []}, "Puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer", "--extension"], "env": {}, "disabled": false, "autoApprove": []}}}
Package Manager Inventory Report
Generated on: 2025-08-22
System: Windows (PowerShell)

=== NPM Global Packages ===
Command: npm list -g --depth=0
Status: SUCCESS

C:\Users\<USER>\AppData\Roaming\npm
├── @google/gemini-cli@0.1.22
├── npm@11.2.0
├── pnpm@10.6.1
└── task-master-ai@0.15.0

Installation Commands:
- npm install -g @google/gemini-cli
- npm install -g npm@latest
- npm install -g pnpm
- npm install -g task-master-ai

=== Python Packages ===
Command: pip list
Status: SUCCESS

Package            Version
------------------ -----------
attrs              25.3.0
blinker            1.9.0
certifi            2025.7.9
cffi               1.17.1
charset-normalizer 3.4.1
click              8.1.8
colorama           0.4.6
Flask              3.1.0
h11                0.16.0
idna               3.10
itsdangerous       2.2.0
Jinja2             3.1.6
MarkupSafe         3.0.2
outcome            1.3.0.post0
pip                25.1.1
pycparser          2.22
pygame             2.6.1
pyotp              2.9.0
PySocks            1.7.1
python-dotenv      1.1.0
requests           2.32.3
selenium           4.34.2
sniffio            1.3.1
sortedcontainers   2.4.0
trio               0.30.0
trio-websocket     0.12.2
typing_extensions  4.14.1
urllib3            2.5.0
watchdog           6.0.0
websocket-client   1.8.0
Werkzeug           3.1.3
wsproto            1.2.0

Installation Commands:
- pip install attrs
- pip install blinker
- pip install certifi
- pip install cffi
- pip install charset-normalizer
- pip install click
- pip install colorama
- pip install Flask
- pip install h11
- pip install idna
- pip install itsdangerous
- pip install Jinja2
- pip install MarkupSafe
- pip install outcome
- pip install --upgrade pip
- pip install pycparser
- pip install pygame
- pip install pyotp
- pip install PySocks
- pip install python-dotenv
- pip install requests
- pip install selenium
- pip install sniffio
- pip install sortedcontainers
- pip install trio
- pip install trio-websocket
- pip install typing_extensions
- pip install urllib3
- pip install watchdog
- pip install websocket-client
- pip install Werkzeug
- pip install wsproto

=== Summary ===
- NPM Global Packages: 4 packages found
- Python Packages: 32 packages found
- Total Packages Inventoried: 36 packages

All package manager commands executed successfully.
